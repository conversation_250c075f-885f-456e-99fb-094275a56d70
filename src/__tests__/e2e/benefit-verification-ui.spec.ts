/**
 * E2E Test for Benefit Verification UI Flow
 * Tests that verify button changes from 'Confirm' to 'Verified' and stays grayed out
 */

import { test, expect } from '@playwright/test'

test.describe('Benefit Verification UI Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/')
  })

  test('should show Confirm button for unverified benefits and change to Verified after verification', async ({ page }) => {
    // Step 1: Sign in as a user who can verify benefits
    await page.click('text=Sign In')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.click('button:has-text("Send Magic Link")')
    
    // Wait for magic link sent confirmation
    await expect(page.locator('text=Magic link sent')).toBeVisible({ timeout: 10000 })
    
    // Simulate clicking the magic link by navigating directly to the verification URL
    // In a real test, you'd extract the token from email, but for E2E we'll use a test token
    await page.goto('/auth/verify?token=test-token-for-testcorp-user')
    
    // Wait for successful sign-in
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to a company page that has benefits
    await page.goto('/companies')
    
    // Find and click on a company (TestCorp should be available)
    await page.click('text=TestCorp')
    
    // Wait for company page to load
    await expect(page.locator('h1')).toContainText('TestCorp')

    // Step 3: Look for a benefit verification section
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')
    
    if (await verificationSection.count() > 0) {
      // Step 4: Check if there's a Confirm button (unverified benefit)
      const confirmButton = verificationSection.locator('button:has-text("Confirm")')
      
      if (await confirmButton.count() > 0) {
        // Verify the button is enabled and has correct styling
        await expect(confirmButton).toBeEnabled()
        await expect(confirmButton).toHaveClass(/bg-green-600/)
        
        // Step 5: Click the Confirm button to verify the benefit
        await confirmButton.click()
        
        // Step 6: Wait for the button to change to "Verified"
        await expect(verificationSection.locator('button:has-text("Verified")')).toBeVisible({ timeout: 10000 })
        
        // Step 7: Verify the button is now disabled and grayed out
        const verifiedButton = verificationSection.locator('button:has-text("Verified")')
        await expect(verifiedButton).toBeDisabled()
        await expect(verifiedButton).toHaveClass(/bg-gray-400/)
        
        // Step 8: Verify the "already verified" message appears
        await expect(verificationSection.locator('text=You have already verified this benefit')).toBeVisible()
        
        // Step 9: Reload the page to ensure the state persists
        await page.reload()
        await expect(page.locator('h1')).toContainText('TestCorp') // Wait for page to load
        
        // Step 10: Verify the button is still "Verified" and grayed out after reload
        const verificationSectionAfterReload = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')
        if (await verificationSectionAfterReload.count() > 0) {
          const verifiedButtonAfterReload = verificationSectionAfterReload.locator('button:has-text("Verified")')
          await expect(verifiedButtonAfterReload).toBeVisible()
          await expect(verifiedButtonAfterReload).toBeDisabled()
          await expect(verifiedButtonAfterReload).toHaveClass(/bg-gray-400/)
          await expect(verificationSectionAfterReload.locator('text=You have already verified this benefit')).toBeVisible()
        }
        
        console.log('✅ Benefit verification UI flow test completed successfully')
      } else {
        // If no Confirm button, check if there's already a Verified button
        const verifiedButton = verificationSection.locator('button:has-text("Verified")')
        if (await verifiedButton.count() > 0) {
          await expect(verifiedButton).toBeDisabled()
          await expect(verifiedButton).toHaveClass(/bg-gray-400/)
          console.log('✅ Benefit already verified - UI state is correct')
        } else {
          console.log('ℹ️ No verification buttons found - user may not be authorized')
        }
      }
    } else {
      console.log('ℹ️ No benefit verification sections found on this company page')
    }
  })

  test('should not show verification section for unauthorized users', async ({ page }) => {
    // Step 1: Sign in as a user from a different company
    await page.click('text=Sign In')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.click('button:has-text("Send Magic Link")')
    
    // Wait for magic link sent confirmation
    await expect(page.locator('text=Magic link sent')).toBeVisible({ timeout: 10000 })
    
    // Simulate clicking the magic link
    await page.goto('/auth/verify?token=test-token-for-different-user')
    
    // Wait for successful sign-in
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to TestCorp company page
    await page.goto('/companies')
    await page.click('text=TestCorp')
    
    // Wait for company page to load
    await expect(page.locator('h1')).toContainText('TestCorp')

    // Step 3: Verify that verification sections are not shown for unauthorized users
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')
    
    // Should not see verification sections since user is not from TestCorp
    await expect(verificationSection).toHaveCount(0)
    
    console.log('✅ Unauthorized user correctly cannot see verification sections')
  })

  test('should handle verification API errors gracefully', async ({ page }) => {
    // Step 1: Sign in as authorized user
    await page.click('text=Sign In')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.click('button:has-text("Send Magic Link")')
    await expect(page.locator('text=Magic link sent')).toBeVisible({ timeout: 10000 })
    await page.goto('/auth/verify?token=test-token-for-testcorp-user')
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to company page
    await page.goto('/companies')
    await page.click('text=TestCorp')
    await expect(page.locator('h1')).toContainText('TestCorp')

    // Step 3: Mock API failure by intercepting the verification request
    await page.route('/api/benefit-verifications', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })

    // Step 4: Try to verify a benefit
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')
    
    if (await verificationSection.count() > 0) {
      const confirmButton = verificationSection.locator('button:has-text("Confirm")')
      
      if (await confirmButton.count() > 0) {
        await confirmButton.click()
        
        // Step 5: Verify error handling (button should not change to Verified)
        await page.waitForTimeout(2000) // Wait a bit for any potential changes
        
        // Button should still be "Confirm" and enabled
        await expect(confirmButton).toBeVisible()
        await expect(confirmButton).toBeEnabled()
        
        // Should not see "Verified" button
        await expect(verificationSection.locator('button:has-text("Verified")')).toHaveCount(0)
        
        console.log('✅ API error handled gracefully - UI state preserved')
      }
    }
  })
})

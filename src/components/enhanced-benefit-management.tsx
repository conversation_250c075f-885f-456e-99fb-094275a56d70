'use client'

import { useState, useEffect, useCallback } from 'react'
import { CheckCircle, Plus, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BenefitVerification } from '@/components/benefit-verification'
import { CompanyVerificationNotice } from '@/components/company-verification-notice'
import { BenefitVerificationCounts } from '@/components/benefit-verification-counts'
import { BenefitVerificationCountsOptimized } from '@/components/benefit-verification-counts-optimized'
import { BatchBenefitSelection } from '@/components/batch-benefit-selection'

import { useCompanyAuthorization } from '@/hooks/use-company-authorization'
import { useBatchBenefitVerifications } from '@/hooks/use-batch-benefit-verifications'

interface CompanyBenefit {
  id: string
  benefit_id: string
  name: string
  category: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  added_by?: string
  created_at: string
}

interface EnhancedBenefitManagementProps {
  companyId: string
  companyName: string
  canManage: boolean
}

export function EnhancedBenefitManagement({ companyId, companyName, canManage }: EnhancedBenefitManagementProps) {
  const [companyBenefits, setCompanyBenefits] = useState<CompanyBenefit[]>([])
  const [loading, setLoading] = useState(false)
  const [showBatchModal, setShowBatchModal] = useState(false)
  const [showDisputeForm, setShowDisputeForm] = useState<string | null>(null) // Track which benefit's dispute form is open
  const [disputeReason, setDisputeReason] = useState('')
  const [isSubmittingDispute, setIsSubmittingDispute] = useState(false)

  // Get all company benefit IDs for batch verification loading
  const companyBenefitIds = companyBenefits.map(cb => cb.id)

  // Use company-level authorization
  const { authStatus, isLoading: isLoadingAuth } = useCompanyAuthorization(companyId)

  // Use batch benefit verifications for better performance
  const { getVerificationCounts, isLoading: isLoadingVerifications } = useBatchBenefitVerifications(companyBenefitIds)

  const fetchCompanyBenefits = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/companies/${companyId}/benefits`)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data)
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
    } finally {
      setLoading(false)
    }
  }, [companyId])

  useEffect(() => {
    fetchCompanyBenefits()
  }, [companyId, fetchCompanyBenefits])



  const handleBatchSuccess = () => {
    fetchCompanyBenefits()
    setShowBatchModal(false)
  }

  const handleRemoveBenefit = async (benefitId: string, benefitName: string, isVerified: boolean, companyBenefitId: string) => {
    if (!isVerified) {
      // For unverified benefits, delete directly
      if (!confirm(`Remove "${benefitName}" from ${companyName}? This will also delete all verifications.`)) {
        return
      }

      try {
        const response = await fetch(`/api/companies/${companyId}/benefits?benefitId=${benefitId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          alert('Benefit removed successfully!')
          fetchCompanyBenefits()
        } else {
          const error = await response.json()
          alert(error.error || 'Failed to remove benefit')
        }
      } catch (error) {
        console.error('Error removing benefit:', error)
        alert('Failed to remove benefit')
      }
    } else {
      // For verified benefits, show dispute form
      setShowDisputeForm(companyBenefitId)
      setDisputeReason('')
    }
  }

  const handleSubmitDispute = async (companyBenefitId: string, benefitName: string) => {
    if (!disputeReason.trim()) {
      alert('Please provide a reason for the dispute')
      return
    }

    setIsSubmittingDispute(true)
    try {
      const response = await fetch('/api/benefit-removal-disputes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId: companyBenefitId,
          reason: disputeReason.trim()
        })
      })

      if (response.ok) {
        alert('Dispute submitted successfully!')
        setShowDisputeForm(null)
        setDisputeReason('')
        fetchCompanyBenefits()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit dispute')
      }
    } catch (error) {
      console.error('Error submitting dispute:', error)
      alert('Failed to submit dispute')
    } finally {
      setIsSubmittingDispute(false)
    }
  }

  // Group benefits by category
  const categorizedBenefits = companyBenefits.reduce((acc, benefit) => {
    const category = benefit.category || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(benefit)
    return acc
  }, {} as Record<string, CompanyBenefit[]>)

  const [categories, setCategories] = useState([
    { key: 'health', label: 'Health & Medical' },
    { key: 'time_off', label: 'Time Off' },
    { key: 'financial', label: 'Financial' },
    { key: 'development', label: 'Development' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'work_life', label: 'Work-Life Balance' },
    { key: 'other', label: 'Other' },
  ])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/benefit-categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.map((cat: { name: string; display_name: string; icon?: string }) => ({
          key: cat.name,
          label: cat.display_name,
          icon: cat.icon
        })))
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      // Keep default categories as fallback
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  // Get existing benefit IDs for the batch selection component
  const existingBenefitIds = companyBenefits.map(cb => cb.benefit_id)

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex-1 min-w-0">
          <h2 className="text-xl font-semibold text-gray-900">Company Benefits</h2>
          <p className="text-sm text-gray-600 mt-1 break-words">
            {canManage
              ? `Manage and verify benefits for ${companyName}. You can add, remove, and verify benefits based on your company email domain.`
              : `View and verify benefits offered by ${companyName}. You can confirm or dispute benefits based on your experience.`
            }
          </p>
        </div>
        {canManage && companyBenefits.length > 0 && (
          <Button
            onClick={() => setShowBatchModal(true)}
            className="flex items-center gap-2 w-full sm:w-auto flex-shrink-0"
          >
            <Plus className="w-4 h-4" />
            Add Benefits
          </Button>
        )}
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading benefits...</p>
        </div>
      ) : companyBenefits.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No benefits have been added for this company yet.</p>
          {canManage && (
            <Button
              onClick={() => setShowBatchModal(true)}
              className="mt-4 flex items-center gap-2 mx-auto w-full sm:w-auto"
            >
              <Plus className="w-4 h-4" />
              Add Benefits
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-8">
          {categories.map((category) => {
            const categoryBenefits = categorizedBenefits[category.key] || []
            if (categoryBenefits.length === 0) {return null}

            return (
              <div key={category.key} className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {category.label}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categoryBenefits.map((benefit) => (
                    <div key={benefit.id} className="space-y-3">
                      <div
                        className={`p-4 rounded-lg border ${
                          benefit.is_verified
                            ? 'bg-green-50 border-green-200'
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between gap-4">
                          <div className="flex items-center space-x-2 flex-1 min-w-0">
                            {benefit.icon && (
                              <span className="text-lg flex-shrink-0">{benefit.icon}</span>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 min-w-0">
                                <h4 className="font-medium text-gray-900 truncate">
                                  {benefit.name}
                                </h4>
                                {benefit.is_verified && (
                                  <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                                )}
                              </div>
                            </div>
                          </div>
                          {canManage && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleRemoveBenefit(benefit.benefit_id, benefit.name, benefit.is_verified, benefit.id)}
                              className="flex items-center justify-center flex-shrink-0 ml-2"
                              title={benefit.is_verified ? "Request removal of this benefit" : "Delete benefit"}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>

                        {/* Verification counts for all users */}
                        {companyBenefitIds.length > 0 ? (
                          <BenefitVerificationCountsOptimized
                            companyBenefitId={benefit.id}
                            verificationCounts={getVerificationCounts(benefit.id)}
                            isLoading={isLoadingVerifications}
                          />
                        ) : (
                          <BenefitVerificationCounts companyBenefitId={benefit.id} />
                        )}



                        {/* Inline Dispute Form - Triggered by unified button */}
                        {showDisputeForm === benefit.id && (
                          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <h5 className="font-medium text-gray-900 mb-2">
                              Request Removal of "{benefit.name}"
                            </h5>
                            <p className="text-sm text-gray-600 mb-3">
                              Please explain why this benefit should be removed from {companyName}:
                            </p>
                            <textarea
                              value={disputeReason}
                              onChange={(e) => setDisputeReason(e.target.value)}
                              placeholder="Explain why this benefit should be removed..."
                              className="w-full p-3 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              rows={4}
                            />
                            <div className="flex gap-2 mt-3">
                              <Button
                                onClick={() => handleSubmitDispute(benefit.id, benefit.name)}
                                disabled={isSubmittingDispute || !disputeReason.trim()}
                                className="bg-yellow-600 hover:bg-yellow-700 text-white"
                              >
                                {isSubmittingDispute ? 'Submitting...' : 'Submit Dispute'}
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setShowDisputeForm(null)
                                  setDisputeReason('')
                                }}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Benefit Verification Component - Only for authorized users */}
                      {!isLoadingAuth && authStatus && authStatus.authorized && (
                        <BenefitVerification
                          companyBenefitId={benefit.id}
                          benefitName={benefit.name}
                          companyName={companyName}
                          onVerificationComplete={fetchCompanyBenefits}
                          companyAuthStatus={authStatus}
                          hideAuthRestriction={true}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}

          {/* Company-level verification notice */}
          {!isLoadingAuth && authStatus && !authStatus.authorized && (
            <div className="mt-8">
              <CompanyVerificationNotice authStatus={authStatus} />
            </div>
          )}
        </div>
      )}

      {/* Batch Benefit Selection Modal */}
      <BatchBenefitSelection
        companyId={companyId}
        companyName={companyName}
        isOpen={showBatchModal}
        onClose={() => setShowBatchModal(false)}
        onSuccess={handleBatchSuccess}
        existingBenefitIds={existingBenefitIds}
      />
    </div>
  )
}
